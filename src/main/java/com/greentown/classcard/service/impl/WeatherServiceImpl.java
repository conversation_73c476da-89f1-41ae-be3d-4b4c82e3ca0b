package com.greentown.classcard.service.impl;

import com.greentown.classcard.component.RestUtil;
import com.greentown.classcard.model.dto.WeatherInfoDTO;
import com.greentown.classcard.service.WeatherService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class WeatherServiceImpl implements WeatherService {

    @Autowired
    RestUtil restUtil;


    @Override
    @Cacheable(value = "weather_live", key = "'live'", unless = "#result == null", cacheManager = "cacheManager")
    public WeatherInfoDTO.Live getLiveWeather() {
        return restUtil.getWeatherInfo().getLives().get(0);
    }

    @Override
    @Cacheable(value = "weather_forecast", key = "'forecast'", unless = "#result == null", cacheManager = "cacheManager")
    public WeatherInfoDTO.Cast getForecastWeather() {
        return restUtil.getForecastWeatherInfo().getForecasts().get(0).getCasts().get(1);
    }
}
