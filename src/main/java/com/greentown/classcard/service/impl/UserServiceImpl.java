package com.greentown.classcard.service.impl;





import com.greentown.classcard.component.RedisUtil;
import com.greentown.classcard.component.RestUtil;
import com.greentown.classcard.enums.UnAuthorizedStateEnum;
import com.greentown.classcard.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.oauth2.core.OAuth2AuthenticationException;
import org.springframework.security.oauth2.core.OAuth2Error;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class UserServiceImpl implements UserService {

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private RestUtil restUtil;

    public static final String FORBIDDEN_STATE = "foundation:user_forbidden_state:";


    public static final String ROLE_CHANGED = "foundation:role_changed:";


    public static final String PASSWORD_CHANGED = "foundation:password_changed:";


    public static final Long REFRESH_TOKEN_VALID_TIME = 2592000L;


    public static final Long ACCESS_TOKEN_VALID_TIME = 3600L;



    @Override
    public void checkUserState(Long id, Long exp) {
        if (checkForbiddenState(id)) {
            OAuth2Error auth2Error = new OAuth2Error("USER_FORBIDDEN");
            throw new OAuth2AuthenticationException(auth2Error, UnAuthorizedStateEnum.FORBIDDEN_USER.getMessage());
        }
        if (checkRoleState(id, exp)) {
            OAuth2Error auth2Error = new OAuth2Error("ROLE_RESET");
            throw new OAuth2AuthenticationException(auth2Error, UnAuthorizedStateEnum.ROLE_RESET_USER.getMessage());
        }
        if (checkPasswordState(id, exp + REFRESH_TOKEN_VALID_TIME - ACCESS_TOKEN_VALID_TIME)) {
            OAuth2Error auth2Error = new OAuth2Error("PASSWORD_RESET");
            throw new OAuth2AuthenticationException(auth2Error, UnAuthorizedStateEnum.PASSWORD_CHANGED_USER.getMessage());
        }
    }

    @Override
    @Cacheable(value = "authority_v2", cacheManager = "cacheManager")
    public Collection<GrantedAuthority> getAuthoritiesByRole(List<Long> roleIds) {
        return restUtil.getAuthorities(roleIds).stream().map(SimpleGrantedAuthority::new).collect(Collectors.toSet());
    }

    public Boolean checkForbiddenState(Long id) {
        return redisUtil.hasKey(FORBIDDEN_STATE + id);
    }

    public Boolean checkRoleState(Long id, Long exp) {
        if (redisUtil.hasKey(ROLE_CHANGED + id)) {
            long time = Long.parseLong(redisUtil.get(ROLE_CHANGED + id).toString());
            return time > exp;
        } else {
            return false;
        }
    }

    public Boolean checkPasswordState(Long id, Long exp) {
        if (redisUtil.hasKey(PASSWORD_CHANGED + id)) {
            long time = Long.parseLong(redisUtil.get(PASSWORD_CHANGED + id).toString());
            return time > exp;
        } else {
            return false;
        }
    }
}
